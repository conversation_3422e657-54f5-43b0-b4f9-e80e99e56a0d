.PHONY: help install backend frontend tunnel

.DEFAULT_GOAL := help
PROJECT_NAME := paykka-duty

help: ## Show this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

dev: ## Run Django server in development mode
	@echo "🚀 Starting Django server..."
	@uv run manage.py tailwind runserver 0.0.0.0:8000

lint: ## Lint code with Ruff
	@echo "🔍 Linting code with Ruff..."
	@uv run ruff check --fix

tunnel: ## Run cloudflared tunnel
	@echo "🚇 Starting cloudflared tunnel..."
	@cloudflared tunnel run --token `cloudflared tunnel token $(PROJECT_NAME)` --protocol http2

keep: ## Run Keep service
	@echo "🚀 Starting Keep service..."
	@docker compose --profile dev up -d keep
