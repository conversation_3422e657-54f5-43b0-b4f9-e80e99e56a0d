from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
  class Gender(models.IntegerChoices):
    UNKNOWN = 0, _("unknown")
    MALE = 1, _("male")
    FEMALE = 2, _("female")

  userid = models.CharField(_("userid"), max_length=150, unique=True)
  alias = models.CharField(_("alias"), max_length=150, blank=True, null=True)
  position = models.Char<PERSON>ield(_("position"), max_length=150, blank=True, null=True)
  avatar_url = models.URLField(_("avatar url"), blank=True, null=True)
  gender = models.IntegerField(
    _("gender"), choices=Gender.choices, default=Gender.UNKNOWN
  )
  mobile = models.Char<PERSON>ield(_("mobile"), max_length=150, blank=True, null=True)
