/*! tailwindcss v4.1.13 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
    monospace;
    --color-black: #000;
    --spacing: 0.25rem;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  ::-webkit-calendar-picker-indicator {
    line-height: 1;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type='button'], [type='reset'], [type='submit']), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden='until-found'])) {
    display: none !important;
  }
}
@layer utilities {
  .btn {
    :where(&) {
      width: unset;
    }
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 1.5);
    text-align: center;
    vertical-align: middle;
    outline-offset: 2px;
    webkit-user-select: none;
    user-select: none;
    padding-inline: var(--btn-p);
    color: var(--btn-fg);
    --tw-prose-links: var(--btn-fg);
    height: var(--size);
    font-size: var(--fontsize, 0.875rem);
    font-weight: 600;
    outline-color: var(--btn-color, var(--color-base-content));
    transition-property: color, background-color, border-color, box-shadow;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transition-duration: 0.2s;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-color: var(--btn-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--btn-noise);
    border-width: var(--border);
    border-style: solid;
    border-color: var(--btn-border);
    text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
    touch-action: manipulation;
    box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
    --size: calc(var(--size-field, 0.25rem) * 10);
    --btn-bg: var(--btn-color, var(--color-base-200));
    --btn-fg: var(--color-base-content);
    --btn-p: 1rem;
    --btn-border: var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
    }
    --btn-shadow: 0 3px 2px -2px var(--btn-bg),
    0 4px 3px -2px var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),
    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
    }
    --btn-noise: var(--fx-noise);
    .prose & {
      text-decoration-line: none;
    }
    @media (hover: hover) {
      &:hover {
        --btn-bg: var(--btn-color, var(--color-base-200));
        @supports (color: color-mix(in lab, red, red)) {
          --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
        }
      }
    }
    &:focus-visible, &:has(:focus-visible) {
      outline-width: 2px;
      outline-style: solid;
      isolation: isolate;
    }
    &:active:not(.btn-active) {
      translate: 0 0.5px;
      --btn-bg: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
      }
      --btn-border: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    }
    &:is(:disabled, [disabled], .btn-disabled) {
      &:not(.btn-link, .btn-ghost) {
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        box-shadow: none;
      }
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
      @media (hover: hover) {
        &:hover {
          pointer-events: none;
          background-color: var(--color-neutral);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
          }
          --btn-border: #0000;
          --btn-fg: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
          }
        }
      }
    }
    &:is(input[type="checkbox"], input[type="radio"]) {
      appearance: none;
      &::after {
        content: attr(aria-label);
      }
    }
    &:where(input:checked:not(.filter .btn)) {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
      isolation: isolate;
    }
  }
  .collapse {
    &:not(td, tr, colgroup) {
      visibility: visible;
    }
    position: relative;
    display: grid;
    overflow: hidden;
    border-radius: var(--radius-box, 1rem);
    width: 100%;
    grid-template-rows: max-content 0fr;
    isolation: isolate;
    @media (prefers-reduced-motion: no-preference) {
      transition: grid-template-rows 0.2s;
    }
    > input:is([type="checkbox"], [type="radio"]) {
      grid-column-start: 1;
      grid-row-start: 1;
      appearance: none;
      opacity: 0;
      z-index: 1;
      width: 100%;
      padding: 1rem;
      padding-inline-end: 3rem;
      min-height: 1lh;
      transition: background-color 0.2s ease-out;
    }
    &:is([open], :focus:not(.collapse-close)), &:not(.collapse-close):has(> input:is([type="checkbox"], [type="radio"]):checked) {
      grid-template-rows: max-content 1fr;
    }
    &:is([open], :focus:not(.collapse-close)) > .collapse-content, &:not(.collapse-close) > :where(input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-content) {
      visibility: visible;
      min-height: fit-content;
    }
    &:focus-visible, &:has(> input:is([type="checkbox"], [type="radio"]):focus-visible) {
      outline-color: var(--color-base-content);
      outline-style: solid;
      outline-width: 2px;
      outline-offset: 2px;
    }
    &:not(.collapse-close) {
      > input[type="checkbox"], > input[type="radio"]:not(:checked), > .collapse-title {
        cursor: pointer;
      }
    }
    &:focus:not(.collapse-close, .collapse[open]) > .collapse-title {
      cursor: unset;
    }
    &:is([open], :focus:not(.collapse-close)) > :where(.collapse-content), &:not(.collapse-close) > :where(input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-content) {
      padding-bottom: 1rem;
      @media (prefers-reduced-motion: no-preference) {
        transition: padding 0.2s ease-out, background-color 0.2s ease-out;
      }
    }
    &:is([open]) {
      &.collapse-arrow {
        > .collapse-title:after {
          @media (prefers-reduced-motion: no-preference) {
            transform: translateY(-50%) rotate(225deg);
          }
        }
      }
    }
    &.collapse-open {
      &.collapse-arrow {
        > .collapse-title:after {
          @media (prefers-reduced-motion: no-preference) {
            transform: translateY(-50%) rotate(225deg);
          }
        }
      }
      &.collapse-plus {
        > .collapse-title:after {
          content: "−";
        }
      }
    }
    &.collapse-arrow:focus:not(.collapse-close) {
      > .collapse-title:after {
        transform: translateY(-50%) rotate(225deg);
      }
    }
    &.collapse-arrow:not(.collapse-close) {
      > input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-title:after {
        transform: translateY(-50%) rotate(225deg);
      }
    }
    &[open] {
      &.collapse-plus {
        > .collapse-title:after {
          content: "−";
        }
      }
    }
    &.collapse-plus:focus:not(.collapse-close) {
      > .collapse-title:after {
        content: "−";
      }
    }
    &.collapse-plus:not(.collapse-close) {
      > input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-title:after {
        content: "−";
      }
    }
    &:is(details) {
      width: 100%;
      & summary {
        position: relative;
        display: block;
        &::-webkit-details-marker {
          display: none;
        }
      }
    }
    &:is(details) summary {
      outline: none;
    }
  }
  .collapse {
    visibility: collapse;
  }
  .visible {
    visibility: visible;
  }
  .list {
    display: flex;
    flex-direction: column;
    font-size: 0.875rem;
    :where(.list-row) {
      --list-grid-cols: minmax(0, auto) 1fr;
      position: relative;
      display: grid;
      grid-auto-flow: column;
      gap: calc(0.25rem * 4);
      border-radius: var(--radius-box);
      padding: calc(0.25rem * 4);
      word-break: break-word;
      grid-template-columns: var(--list-grid-cols);
      &:has(.list-col-grow:nth-child(1)) {
        --list-grid-cols: 1fr;
      }
      &:has(.list-col-grow:nth-child(2)) {
        --list-grid-cols: minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(3)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(4)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(5)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(6)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)
        minmax(0, auto) 1fr;
      }
      :not(.list-col-wrap) {
        grid-row-start: 1;
      }
    }
    & > :not(:last-child) {
      &.list-row, .list-row {
        &:after {
          content: "";
          border-bottom: var(--border) solid;
          inset-inline: var(--radius-box);
          position: absolute;
          bottom: calc(0.25rem * 0);
          border-color: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
          }
        }
      }
    }
  }
  .toggle {
    border: var(--border) solid currentColor;
    color: var(--input-color);
    position: relative;
    display: inline-grid;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    grid-template-columns: 0fr 1fr 1fr;
    --radius-selector-max: calc(
    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)
  );
    border-radius: calc( var(--radius-selector) + min(var(--toggle-p), var(--radius-selector-max)) + min(var(--border), var(--radius-selector-max)) );
    padding: var(--toggle-p);
    box-shadow: 0 1px currentColor inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000) inset;
    }
    transition: color 0.3s, grid-template-columns 0.2s;
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 50%, #0000);
    }
    --toggle-p: calc(var(--size) * 0.125);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: calc((var(--size) * 2) - (var(--border) + var(--toggle-p)) * 2);
    height: var(--size);
    > * {
      z-index: 1;
      grid-column: span 1 / span 1;
      grid-column-start: 2;
      grid-row-start: 1;
      height: 100%;
      cursor: pointer;
      appearance: none;
      background-color: transparent;
      padding: calc(0.25rem * 0.5);
      transition: opacity 0.2s, rotate 0.4s;
      border: none;
      &:focus {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:nth-child(2) {
        color: var(--color-base-100);
        rotate: 0deg;
      }
      &:nth-child(3) {
        color: var(--color-base-100);
        opacity: 0%;
        rotate: -15deg;
      }
    }
    &:has(:checked) {
      > :nth-child(2) {
        opacity: 0%;
        rotate: 15deg;
      }
      > :nth-child(3) {
        opacity: 100%;
        rotate: 0deg;
      }
    }
    &:before {
      position: relative;
      inset-inline-start: calc(0.25rem * 0);
      grid-column-start: 2;
      grid-row-start: 1;
      aspect-ratio: 1 / 1;
      height: 100%;
      border-radius: var(--radius-selector);
      background-color: currentColor;
      translate: 0;
      --tw-content: "";
      content: var(--tw-content);
      transition: background-color 0.1s, translate 0.2s, inset-inline-start 0.2s;
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px currentColor;
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000);
      }
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    @media (forced-colors: active) {
      &:before {
        outline-style: var(--tw-outline-style);
        outline-width: 1px;
        outline-offset: calc(1px * -1);
      }
    }
    @media print {
      &:before {
        outline: 0.25rem solid;
        outline-offset: -1rem;
      }
    }
    &:focus-visible, &:has(:focus-visible) {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"], &:has(> input:checked) {
      grid-template-columns: 1fr 1fr 0fr;
      background-color: var(--color-base-100);
      --input-color: var(--color-base-content);
      &:before {
        background-color: currentColor;
      }
      @starting-style {
        &:before {
          opacity: 0;
        }
      }
    }
    &:indeterminate {
      grid-template-columns: 0.5fr 1fr 0.5fr;
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 30%;
      &:before {
        background-color: transparent;
        border: var(--border) solid currentColor;
      }
    }
  }
  .input {
    cursor: text;
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 2);
    background-color: var(--color-base-100);
    padding-inline: calc(0.25rem * 3);
    vertical-align: middle;
    white-space: nowrap;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    &:where(input) {
      display: inline-flex;
    }
    :where(input) {
      display: inline-flex;
      height: 100%;
      width: 100%;
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where(input[type="url"]), :where(input[type="email"]) {
      direction: ltr;
    }
    :where(input[type="date"]) {
      display: inline-flex;
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]), fieldset:disabled & {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
    }
    &:has(> input[disabled]) > input[disabled] {
      cursor: not-allowed;
    }
    &::-webkit-date-and-time-value {
      text-align: inherit;
    }
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
    &::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
    }
    &:has(> input[type="date"]) {
      :where(input[type="date"]) {
        display: inline-flex;
        webkit-appearance: none;
        appearance: none;
      }
      input[type="date"]::-webkit-calendar-picker-indicator {
        position: absolute;
        inset-inline-end: 0.75em;
        width: 1em;
        height: 1em;
        cursor: pointer;
      }
    }
  }
  .select {
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    background-color: var(--color-base-100);
    padding-inline-start: calc(0.25rem * 3);
    padding-inline-end: calc(0.25rem * 7);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-image: linear-gradient(45deg, #0000 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, #0000 50%);
    background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16.1px) calc(1px + 50%);
    background-size: 4px 4px, 4px 4px;
    background-repeat: no-repeat;
    text-overflow: ellipsis;
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    border-color: var(--input-color);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    [dir="rtl"] & {
      background-position: calc(0% + 12px) calc(1px + 50%), calc(0% + 16px) calc(1px + 50%);
    }
    select {
      margin-inline-start: calc(0.25rem * -3);
      margin-inline-end: calc(0.25rem * -7);
      width: calc(100% + 2.75rem);
      appearance: none;
      padding-inline-start: calc(0.25rem * 3);
      padding-inline-end: calc(0.25rem * 7);
      height: calc(100% - calc(var(--border) * 2));
      align-items: center;
      background: inherit;
      border-radius: inherit;
      border-style: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * -5.5);
        background-image: none;
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> select[disabled]), &:is(:disabled, [disabled]), fieldset:disabled & {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    &:has(> select[disabled]) > select[disabled] {
      cursor: not-allowed;
    }
    &, & select {
      @supports (appearance: base-select) {
        appearance: base-select;
      }
      @supports (appearance: base-select) {
        &::picker(select) {
          appearance: base-select;
        }
      }
      &::picker(select) {
        color: inherit;
        max-height: min(24rem, 70dvh);
        border: var(--border) solid var(--color-base-200);
        margin-block: calc(0.25rem * 2);
        border-radius: var(--radius-box);
        padding: calc(0.25rem * 2);
        background-color: inherit;
        box-shadow: 0 2px calc(var(--depth) * 3px) -2px oklch(0% 0 0/0.2);
        box-shadow: 0 20px 25px -5px rgb(0 0 0 / calc(var(--depth) * 0.1)), 0 8px 10px -6px rgb(0 0 0 / calc(var(--depth) * 0.1));
      }
      &::picker-icon {
        display: none;
      }
      optgroup {
        padding-top: 0.5em;
        option {
          &:nth-child(1) {
            margin-top: 0.5em;
          }
        }
      }
      option {
        border-radius: var(--radius-field);
        padding-inline: calc(0.25rem * 3);
        padding-block: calc(0.25rem * 1.5);
        transition-property: color, background-color;
        transition-duration: 0.2s;
        transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
        &:not(:disabled) {
          &:hover, &:focus-visible {
            cursor: pointer;
            background-color: var(--color-base-content);
            @supports (color: color-mix(in lab, red, red)) {
              background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
            }
            --tw-outline-style: none;
            outline-style: none;
            @media (forced-colors: active) {
              outline: 2px solid transparent;
              outline-offset: 2px;
            }
          }
          &:active {
            background-color: var(--color-neutral);
            color: var(--color-neutral-content);
            box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--color-neutral);
          }
        }
      }
    }
  }
  .card {
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-box);
    outline-width: 2px;
    transition: outline 0.2s ease-in-out;
    outline: 0 solid #0000;
    outline-offset: 2px;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline-color: currentColor;
    }
    :where(figure:first-child) {
      overflow: hidden;
      border-start-start-radius: inherit;
      border-start-end-radius: inherit;
      border-end-start-radius: unset;
      border-end-end-radius: unset;
    }
    :where(figure:last-child) {
      overflow: hidden;
      border-start-start-radius: unset;
      border-start-end-radius: unset;
      border-end-start-radius: inherit;
      border-end-end-radius: inherit;
    }
    &:where(.card-border) {
      border: var(--border) solid var(--color-base-200);
    }
    &:where(.card-dash) {
      border: var(--border) dashed var(--color-base-200);
    }
    &.image-full {
      display: grid;
      > * {
        grid-column-start: 1;
        grid-row-start: 1;
      }
      > .card-body {
        position: relative;
        color: var(--color-neutral-content);
      }
      :where(figure) {
        overflow: hidden;
        border-radius: inherit;
      }
      > figure img {
        height: 100%;
        object-fit: cover;
        filter: brightness(28%);
      }
    }
    figure {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &:has(> input:is(input[type="checkbox"], input[type="radio"])) {
      cursor: pointer;
      user-select: none;
    }
    &:has(> :checked) {
      outline: 2px solid currentColor;
    }
  }
  .avatar {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & > div {
      display: block;
      aspect-ratio: 1 / 1;
      overflow: hidden;
    }
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
  .checkbox {
    border: var(--border) solid var(--input-color, var(--color-base-content));
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));
    }
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: var(--radius-selector);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    color: var(--color-base-content);
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0 #0000 inset, 0 0 #0000;
    transition: background-color 0.2s, box-shadow 0.2s;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    &:before {
      --tw-content: "";
      content: var(--tw-content);
      display: block;
      width: 100%;
      height: 100%;
      rotate: 45deg;
      background-color: currentColor;
      opacity: 0%;
      transition: clip-path 0.3s, opacity 0.1s, rotate 0.3s, translate 0.3s;
      transition-delay: 0.1s;
      clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);
      box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      font-size: 1rem;
      line-height: 0.75;
    }
    &:focus-visible {
      outline: 2px solid var(--input-color, currentColor);
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"] {
      background-color: var(--input-color, #0000);
      box-shadow: 0 0 #0000 inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      &:before {
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
        opacity: 100%;
      }
      @media (forced-colors: active) {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
      @media print {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
    }
    &:indeterminate {
      background-color: var(--input-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        background-color: var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));
      }
      &:before {
        rotate: 0deg;
        opacity: 100%;
        translate: 0 -35%;
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .radio {
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: calc(infinity * 1px);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    border: var(--border) solid var(--input-color, currentColor);
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in srgb, currentColor 20%, #0000));
    }
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    color: var(--input-color, currentColor);
    &:before {
      display: block;
      width: 100%;
      height: 100%;
      border-radius: calc(infinity * 1px);
      --tw-content: "";
      content: var(--tw-content);
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    &:focus-visible {
      outline: 2px solid currentColor;
    }
    &:checked, &[aria-checked="true"] {
      border-color: currentColor;
      background-color: var(--color-base-100);
      @media (prefers-reduced-motion: no-preference) {
        animation: radio 0.2s ease-out;
      }
      &:before {
        background-color: currentColor;
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      }
      @media (forced-colors: active) {
        &:before {
          outline-style: var(--tw-outline-style);
          outline-width: 1px;
          outline-offset: calc(1px * -1);
        }
      }
      @media print {
        &:before {
          outline: 0.25rem solid;
          outline-offset: -1rem;
        }
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .static {
    position: static;
  }
  .status {
    display: inline-block;
    aspect-ratio: 1 / 1;
    width: calc(0.25rem * 2);
    height: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    background-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in srgb, #000 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );
    box-shadow: 0 2px 3px -1px currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
    }
  }
  .card-body {
    display: flex;
    flex: auto;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    padding: var(--card-p, 1.5rem);
    font-size: var(--card-fs, 0.875rem);
    :where(p) {
      flex-grow: 1;
    }
  }
  .card-actions {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: calc(0.25rem * 2);
  }
  .card-title {
    display: flex;
    align-items: center;
    gap: calc(0.25rem * 2);
    font-size: var(--cardtitle-fs, 1.125rem);
    font-weight: 600;
  }
  .mask {
    display: inline-block;
    vertical-align: middle;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
  }
  .block {
    display: block;
  }
  .inline {
    display: inline;
  }
  .h-svh {
    height: 100svh;
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .justify-end {
    justify-content: flex-end;
  }
  .bg-base-100 {
    background-color: var(--color-base-100);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .btn-primary {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
  }
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @media (prefers-color-scheme: dark) {
    :root:not([data-theme]) {
      color-scheme: dark;
      --color-base-100: oklch(25.33% 0.016 252.42);
      --color-base-200: oklch(23.26% 0.014 253.1);
      --color-base-300: oklch(21.15% 0.012 254.09);
      --color-base-content: oklch(97.807% 0.029 256.847);
      --color-primary: oklch(58% 0.233 277.117);
      --color-primary-content: oklch(96% 0.018 272.314);
      --color-secondary: oklch(65% 0.241 354.308);
      --color-secondary-content: oklch(94% 0.028 342.258);
      --color-accent: oklch(77% 0.152 181.912);
      --color-accent-content: oklch(38% 0.063 188.416);
      --color-neutral: oklch(14% 0.005 285.823);
      --color-neutral-content: oklch(92% 0.004 286.32);
      --color-info: oklch(74% 0.16 232.661);
      --color-info-content: oklch(29% 0.066 243.157);
      --color-success: oklch(76% 0.177 163.223);
      --color-success-content: oklch(37% 0.077 168.94);
      --color-warning: oklch(82% 0.189 84.429);
      --color-warning-content: oklch(41% 0.112 45.904);
      --color-error: oklch(71% 0.194 13.428);
      --color-error-content: oklch(27% 0.105 12.094);
      --radius-selector: 0.5rem;
      --radius-field: 0.25rem;
      --radius-box: 0.5rem;
      --size-selector: 0.25rem;
      --size-field: 0.25rem;
      --border: 1px;
      --depth: 1;
      --noise: 0;
    }
  }
}
@layer base {
  :root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {
    color-scheme: dark;
    --color-base-100: oklch(25.33% 0.016 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :where( :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked ) ) {
    scrollbar-gutter: stable;
    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));
    --root-bg: var(--color-base-100);
    @supports (color: color-mix(in lab, red, red)) {
      --root-bg: color-mix(in srgb, var(--color-base-100), oklch(0% 0 0) 40%);
    }
  }
  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {
    scrollbar-gutter: stable;
  }
}
@layer base {
  @property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
  }
}
@layer base {
  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 200'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='200' height='200' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }
}
@layer base {
  :root, [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }
}
@layer base {
  :root {
    scrollbar-color: currentColor #0000;
    @supports (color: color-mix(in lab, red, red)) {
      scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
    }
  }
}
@layer base {
  :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*="drawer-open"]) > .drawer-toggle:checked ) {
    overflow: hidden;
  }
}
@keyframes dropdown {
  0% {
    opacity: 0;
  }
}
@keyframes radio {
  0% {
    padding: 5px;
  }
  50% {
    padding: 3px;
  }
}
@keyframes toast {
  0% {
    scale: 0.9;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}
@keyframes rating {
  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
@keyframes skeleton {
  0% {
    background-position: 150%;
  }
  100% {
    background-position: -50%;
  }
}
@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
    }
  }
}
