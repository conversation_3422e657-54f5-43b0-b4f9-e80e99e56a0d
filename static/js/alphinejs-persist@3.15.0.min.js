(()=>{function d(t){let a=()=>{let r,n;try{n=localStorage}catch(i){console.error(i),console.warn("Alpine: $persist is using temporary storage since localStorage is unavailable.");let e=new Map;n={getItem:e.get.bind(e),setItem:e.set.bind(e)}}return t.interceptor((i,e,l,s,f)=>{let o=r||`_x_${s}`,u=g(o,n)?p(o,n):i;return l(u),t.effect(()=>{let c=e();m(o,c,n),l(c)}),u},i=>{i.as=e=>(r=e,i),i.using=e=>(n=e,i)})};Object.defineProperty(t,"$persist",{get:()=>a()}),t.magic("persist",a),t.persist=(r,{get:n,set:i},e=localStorage)=>{let l=g(r,e)?p(r,e):n();i(l),t.effect(()=>{let s=n();m(r,s,e),i(s)})}}function g(t,a){return a.getItem(t)!==null}function p(t,a){let r=a.getItem(t);if(r!==void 0)return JSON.parse(r)}function m(t,a,r){r.setItem(t,JSON.stringify(a))}document.addEventListener("alpine:init",()=>{window.Alpine.plugin(d)});})();
